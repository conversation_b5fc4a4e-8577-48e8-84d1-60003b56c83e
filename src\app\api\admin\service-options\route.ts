import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  validateRequest,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  ApiError
} from '@/lib/api-utils'
import { createServiceOptionSchema } from '@/lib/validations'

// GET /api/admin/service-options - List all service options with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, serviceId } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}

  // Filter by service if provided
  if (serviceId) {
    where.servid = BigInt(serviceId)
  }

  // Add search functionality
  if (search && search.trim()) {
    const searchQuery = buildSearchQuery(search.trim(), ['optname', 'optdesc'])
    Object.assign(where, searchQuery)
  }

  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder, 'createdat')

  // Get service options with pagination
  const [serviceOptions, total] = await Promise.all([
    prisma.serviceoptions.findMany({
      where,
      orderBy: sortQuery,
      skip,
      take,
      include: {
        services: {
          select: {
            id: true,
            name: true,
            categories: {
              select: {
                id: true,
                categname: true,
              },
            },
          },
        },
        serviceoptionfeatures: {
          select: {
            id: true,
            featname: true,
            featcost: true,
            isincluded: true,
          },
          orderBy: {
            createdat: 'asc',
          },
        },
        _count: {
          select: {
            serviceoptionfeatures: true,
            orderdetails: true,
          },
        },
      },
    }),
    prisma.serviceoptions.count({ where })
  ])

  // Transform the response data
  const transformedOptions = serviceOptions.map(option => ({
    id: String(option.id),
    serviceId: String(option.servid),
    name: option.optname,
    description: option.optdesc,
    price: option.optprice ? Number(option.optprice) : null,
    discountRate: option.optdiscountrate,
    totalDiscount: option.opttotaldiscount,
    isActive: option.isactive,
    createdAt: option.createdat,
    updatedAt: option.updatedat,
    service: option.services ? {
      id: String(option.services.id),
      name: option.services.name,
      category: option.services.categories ? {
        id: String(option.services.categories.id),
        name: option.services.categories.categname,
      } : null,
    } : null,
    features: option.serviceoptionfeatures.map(feature => ({
      id: String(feature.id),
      name: feature.featname,
      cost: feature.featcost,
      isIncluded: feature.isincluded,
    })),
    _count: {
      features: option._count.serviceoptionfeatures,
      orderDetails: option._count.orderdetails,
    },
  }))

  return paginatedResponse(transformedOptions, page, limit, total)
})

// POST /api/admin/service-options - Create a new service option
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const validate = validateRequest(createServiceOptionSchema)
  const data = await validate(request)

  // Check if service exists
  const service = await prisma.services.findUnique({
    where: { id: Number(data.serviceId) },
  })

  if (!service) {
    throw new ApiError('Service not found', 404)
  }

  const serviceOption = await prisma.serviceoptions.create({
    data: {
      servid: BigInt(data.serviceId),
      optname: data.name,
      optdesc: data.description,
      optprice: data.price,
      optdiscountrate: data.discountRate,
      opttotaldiscount: data.totalDiscount,
      isactive: data.isActive,
    },
    include: {
      services: {
        select: {
          id: true,
          name: true,
          categories: {
            select: {
              id: true,
              categname: true,
            },
          },
        },
      },
      _count: {
        select: {
          serviceoptionfeatures: true,
          orderdetails: true,
        },
      },
    },
  })

  // Transform the response data
  const transformedOption = {
    id: String(serviceOption.id),
    serviceId: String(serviceOption.servid),
    name: serviceOption.optname,
    description: serviceOption.optdesc,
    price: serviceOption.optprice ? Number(serviceOption.optprice) : null,
    discountRate: serviceOption.optdiscountrate,
    totalDiscount: serviceOption.opttotaldiscount,
    isActive: serviceOption.isactive,
    createdAt: serviceOption.createdat,
    updatedAt: serviceOption.updatedat,
    service: serviceOption.services ? {
      id: String(serviceOption.services.id),
      name: serviceOption.services.name,
      category: serviceOption.services.categories ? {
        id: String(serviceOption.services.categories.id),
        name: serviceOption.services.categories.categname,
      } : null,
    } : null,
    features: [],
    _count: {
      features: serviceOption._count.serviceoptionfeatures,
      orderDetails: serviceOption._count.orderdetails,
    },
  }

  return successResponse(transformedOption, 'Service option created successfully', 201)
})

// DELETE /api/admin/service-options - Bulk delete service options
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { ids } = await request.json()

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new ApiError('Invalid or empty IDs array', 400)
  }

  // Check if any options are being used in orders
  const usedOptions = await prisma.orderdetails.findMany({
    where: { optid: { in: ids.map(Number) } },
    select: { optid: true },
  })

  if (usedOptions.length > 0) {
    const usedIds = usedOptions.map(order => order.optid)
    throw new ApiError(
      `Cannot delete service options that are used in orders. Options with IDs: ${usedIds.join(', ')}`,
      400
    )
  }

  // Delete service option features first
  await prisma.serviceoptionfeatures.deleteMany({
    where: { optid: { in: ids.map(Number) } },
  })

  // Delete service options
  const result = await prisma.serviceoptions.deleteMany({
    where: { id: { in: ids.map(Number) } },
  })

  return successResponse(
    { deletedCount: result.count },
    `${result.count} service option(s) deleted successfully`
  )
})
