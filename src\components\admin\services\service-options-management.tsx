'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ListBulletIcon,
  CurrencyDollarIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import { safeToLocaleDateString } from '@/lib/date-utils'

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  features?: OptionFeature[]
  _count?: {
    features: number
    orderDetails: number
  }
}

interface OptionFeature {
  id: string
  name: string
  cost?: number
  isIncluded: boolean
}

interface ServiceOptionsManagementProps {
  service: Service
  selectedOption: ServiceOption | null
  onOptionSelect: (option: ServiceOption | null) => void
}

interface OptionFormData {
  name: string
  description: string
  price: number
  discountRate: number
  totalDiscount: number
  isActive: boolean
}

export function ServiceOptionsManagement({ service, selectedOption, onOptionSelect }: ServiceOptionsManagementProps) {
  const [options, setOptions] = useState<ServiceOption[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingOption, setEditingOption] = useState<ServiceOption | null>(null)
  const [expandedOptions, setExpandedOptions] = useState<Set<string>>(new Set())
  const [formData, setFormData] = useState<OptionFormData>({
    name: '',
    description: '',
    price: 0,
    discountRate: 0,
    totalDiscount: 0,
    isActive: true
  })

  useEffect(() => {
    if (service) {
      fetchOptions()
    }
  }, [service])

  const fetchOptions = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/service-options?serviceId=${service.id}&limit=100`)
      if (response.ok) {
        const data = await response.json()
        setOptions(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching service options:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingOption 
        ? `/api/admin/service-options/${editingOption.id}`
        : '/api/admin/service-options'
      
      const method = editingOption ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serviceId: service.id,
          name: formData.name,
          description: formData.description,
          price: formData.price,
          discountRate: formData.discountRate,
          totalDiscount: formData.totalDiscount,
          isActive: formData.isActive
        }),
      })

      if (response.ok) {
        await fetchOptions()
        setIsFormOpen(false)
        setEditingOption(null)
        resetForm()
      }
    } catch (error) {
      console.error('Error saving service option:', error)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: 0,
      discountRate: 0,
      totalDiscount: 0,
      isActive: true
    })
  }

  const handleEdit = (option: ServiceOption) => {
    setEditingOption(option)
    setFormData({
      name: option.name,
      description: option.description || '',
      price: option.price || 0,
      discountRate: option.discountRate || 0,
      totalDiscount: option.totalDiscount || 0,
      isActive: option.isActive
    })
    setIsFormOpen(true)
  }

  const handleDelete = async (option: ServiceOption) => {
    if (!confirm(`Are you sure you want to delete "${option.name}"?`)) return

    try {
      const response = await fetch(`/api/admin/service-options/${option.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchOptions()
        if (selectedOption?.id === option.id) {
          onOptionSelect(null)
        }
      }
    } catch (error) {
      console.error('Error deleting service option:', error)
    }
  }

  const toggleExpanded = (optionId: string) => {
    const newExpanded = new Set(expandedOptions)
    if (newExpanded.has(optionId)) {
      newExpanded.delete(optionId)
    } else {
      newExpanded.add(optionId)
    }
    setExpandedOptions(newExpanded)
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-200 rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Options for "{service.name}"
          </h2>
          <p className="text-gray-600">Manage service options and their configurations</p>
        </div>
        <button
          onClick={() => {
            setEditingOption(null)
            resetForm()
            setIsFormOpen(true)
          }}
          className="flex items-center space-x-2 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Option</span>
        </button>
      </div>

      {/* Options List */}
      <div className="space-y-3">
        {options.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <ListBulletIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No options found for this service. Create your first option to get started.</p>
          </div>
        ) : (
          options.map(option => {
            const isExpanded = expandedOptions.has(option.id)
            const isSelected = selectedOption?.id === option.id

            return (
              <motion.div
                key={option.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`border-2 rounded-lg transition-all ${
                  isSelected 
                    ? 'border-orange-500 bg-orange-50' 
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
              >
                <div
                  className="p-4 cursor-pointer"
                  onClick={() => onOptionSelect(option)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleExpanded(option.id)
                        }}
                        className="p-1 hover:bg-gray-200 rounded"
                      >
                        {isExpanded ? (
                          <ChevronDownIcon className="h-4 w-4 text-gray-500" />
                        ) : (
                          <ChevronRightIcon className="h-4 w-4 text-gray-500" />
                        )}
                      </button>

                      <div className="p-2 bg-orange-100 rounded-lg">
                        <ListBulletIcon className="h-5 w-5 text-orange-600" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className={`font-semibold ${
                              isSelected ? 'text-orange-900' : 'text-gray-900'
                            }`}>
                              {option.name}
                            </h3>
                            {option.description && (
                              <p className={`text-sm ${
                                isSelected ? 'text-orange-700' : 'text-gray-600'
                              }`}>
                                {option.description}
                              </p>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-4">
                            {option.price && (
                              <div className="flex items-center space-x-1 text-sm text-gray-600">
                                <CurrencyDollarIcon className="h-4 w-4" />
                                <span>${option.price}</span>
                              </div>
                            )}
                            
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              option.isActive 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {option.isActive ? 'Active' : 'Inactive'}
                            </span>
                            
                            {option._count && (
                              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                {option._count.features} features
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-1 ml-4">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEdit(option)
                        }}
                        className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(option)
                        }}
                        className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {isExpanded && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="border-t border-gray-200 p-4 bg-gray-50"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Created:</span>
                        <p className="text-gray-600">
                          {safeToLocaleDateString(option.createdAt)}
                        </p>
                      </div>
                      
                      {option.discountRate && (
                        <div>
                          <span className="font-medium text-gray-700">Discount:</span>
                          <p className="text-gray-600">{option.discountRate}%</p>
                        </div>
                      )}
                      
                      {option._count && (
                        <div>
                          <span className="font-medium text-gray-700">Usage:</span>
                          <p className="text-gray-600">
                            {option._count.orderDetails} orders
                          </p>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </motion.div>
            )
          })
        )}
      </div>

      {/* Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg p-6 w-full max-w-lg mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                {editingOption ? 'Edit Service Option' : 'Add New Service Option'}
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Option Name
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price ($)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Discount Rate (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={formData.discountRate}
                      onChange={(e) => setFormData({ ...formData, discountRate: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex items-center">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.isActive}
                      onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="text-sm font-medium text-gray-700">Active</span>
                  </label>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    {editingOption ? 'Update' : 'Create'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsFormOpen(false)}
                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
