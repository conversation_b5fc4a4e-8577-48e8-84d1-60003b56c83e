import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse, 
  validateRequest,
  requireAdmin,
  ApiError
} from '@/lib/api-utils'
import { updateServiceOptionSchema } from '@/lib/validations'

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

// GET /api/admin/service-options/[id] - Get a specific service option
export const GET = withError<PERSON>and<PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  const serviceOption = await prisma.serviceoptions.findUnique({
    where: { id: BigInt(id) },
    include: {
      services: {
        select: {
          id: true,
          name: true,
          categories: {
            select: {
              id: true,
              categname: true,
            },
          },
        },
      },
      serviceoptionfeatures: {
        orderBy: {
          createdat: 'asc',
        },
      },
      _count: {
        select: {
          serviceoptionfeatures: true,
          orderdetails: true,
        },
      },
    },
  })

  if (!serviceOption) {
    throw new ApiError('Service option not found', 404)
  }

  // Transform the response data
  const transformedOption = {
    id: String(serviceOption.id),
    serviceId: String(serviceOption.servid),
    name: serviceOption.optname,
    description: serviceOption.optdesc,
    price: serviceOption.optprice ? Number(serviceOption.optprice) : null,
    discountRate: serviceOption.optdiscountrate,
    totalDiscount: serviceOption.opttotaldiscount,
    isActive: serviceOption.isactive,
    createdAt: serviceOption.createdat,
    updatedAt: serviceOption.updatedat,
    service: serviceOption.services ? {
      id: String(serviceOption.services.id),
      name: serviceOption.services.name,
      category: serviceOption.services.categories ? {
        id: String(serviceOption.services.categories.id),
        name: serviceOption.services.categories.categname,
      } : null,
    } : null,
    features: serviceOption.serviceoptionfeatures.map(feature => ({
      id: String(feature.id),
      name: feature.featname,
      description: feature.featdesc,
      cost: feature.featcost,
      discountRate: feature.featdiscountrate,
      totalDiscount: feature.feattotaldiscount,
      isIncluded: feature.isincluded,
      createdAt: feature.createdat,
      updatedAt: feature.updatedat,
    })),
    _count: {
      features: serviceOption._count.serviceoptionfeatures,
      orderDetails: serviceOption._count.orderdetails,
    },
  }

  return successResponse(transformedOption)
})

// PUT /api/admin/service-options/[id] - Update a specific service option
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  const validate = validateRequest(updateServiceOptionSchema)
  const data = await validate(request)

  // Check if service option exists
  const existingOption = await prisma.serviceoptions.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingOption) {
    throw new ApiError('Service option not found', 404)
  }

  // If serviceId is being changed, check if the new service exists
  if (data.serviceId && data.serviceId !== String(existingOption.servid)) {
    const service = await prisma.services.findUnique({
      where: { id: Number(data.serviceId) },
    })

    if (!service) {
      throw new ApiError('Service not found', 404)
    }
  }

  // Prepare update data
  const updateData: any = {
    updatedat: new Date(),
  }

  if (data.serviceId) updateData.servid = Number(data.serviceId)
  if (data.name) updateData.optname = data.name
  if (data.description !== undefined) updateData.optdesc = data.description
  if (data.price !== undefined) updateData.optprice = data.price
  if (data.discountRate !== undefined) updateData.optdiscountrate = data.discountRate
  if (data.totalDiscount !== undefined) updateData.opttotaldiscount = data.totalDiscount
  if (data.isActive !== undefined) updateData.isactive = data.isActive

  const updatedOption = await prisma.serviceoptions.update({
    where: { id: BigInt(id) },
    data: updateData,
    include: {
      services: {
        select: {
          id: true,
          name: true,
          categories: {
            select: {
              id: true,
              categname: true,
            },
          },
        },
      },
      serviceoptionfeatures: {
        select: {
          id: true,
          featname: true,
          featcost: true,
          isincluded: true,
        },
        orderBy: {
          createdat: 'asc',
        },
      },
      _count: {
        select: {
          serviceoptionfeatures: true,
          orderdetails: true,
        },
      },
    },
  })

  // Transform the response data
  const transformedOption = {
    id: String(updatedOption.id),
    serviceId: String(updatedOption.servid),
    name: updatedOption.optname,
    description: updatedOption.optdesc,
    price: updatedOption.optprice ? Number(updatedOption.optprice) : null,
    discountRate: updatedOption.optdiscountrate,
    totalDiscount: updatedOption.opttotaldiscount,
    isActive: updatedOption.isactive,
    createdAt: updatedOption.createdat,
    updatedAt: updatedOption.updatedat,
    service: updatedOption.services ? {
      id: String(updatedOption.services.id),
      name: updatedOption.services.name,
      category: updatedOption.services.categories ? {
        id: String(updatedOption.services.categories.id),
        name: updatedOption.services.categories.categname,
      } : null,
    } : null,
    features: updatedOption.serviceoptionfeatures.map(feature => ({
      id: String(feature.id),
      name: feature.featname,
      cost: feature.featcost,
      isIncluded: feature.isincluded,
    })),
    _count: {
      features: updatedOption._count.serviceoptionfeatures,
      orderDetails: updatedOption._count.orderdetails,
    },
  }

  return successResponse(transformedOption, 'Service option updated successfully')
})

// DELETE /api/admin/service-options/[id] - Delete a specific service option
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if service option exists
  const existingOption = await prisma.serviceoptions.findUnique({
    where: { id: BigInt(id) },
    include: {
      orderdetails: { select: { id: true } },
      serviceoptionfeatures: { select: { id: true } },
    },
  })

  if (!existingOption) {
    throw new ApiError('Service option not found', 404)
  }

  // Check if option is being used in orders
  if (existingOption.orderdetails.length > 0) {
    throw new ApiError(
      'Cannot delete service option that is used in orders. Please remove it from orders first.',
      400
    )
  }

  // Delete service option features first
  if (existingOption.serviceoptionfeatures.length > 0) {
    await prisma.serviceoptionfeatures.deleteMany({
      where: { optid: BigInt(id) },
    })
  }

  // Delete the service option
  await prisma.serviceoptions.delete({
    where: { id: BigInt(id) },
  })

  return successResponse(null, 'Service option deleted successfully')
})
