import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  ApiError
} from '@/lib/api-utils'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/services/[id] - Get a specific service
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  const service = await prisma.services.findUnique({
    where: { id: BigInt(id) },
    include: {
      categories: {
        select: {
          id: true,
          categname: true,
          categdesc: true,
        },
      },
      serviceoptions: {
        include: {
          serviceoptionfeatures: true,
        },
        orderBy: {
          createdat: 'asc',
        },
      },
      _count: {
        select: {
          orderdetails: true,
          serviceoptions: true,
        },
      },
    },
  })

  if (!service) {
    throw new ApiError('Service not found', 404)
  }

  // Transform the response data
  const transformedService = {
    id: String(service.id),
    categoryId: String(service.categid),
    name: service.name,
    description: service.description,
    iconClass: service.iconclass,
    price: service.price,
    discountRate: service.discountrate,
    totalDiscount: service.totaldiscount,
    manager: service.manager,
    isActive: service.isactive,
    displayOrder: service.displayorder,
    createdAt: service.createdat,
    updatedAt: service.updatedat,
    category: service.categories ? {
      id: String(service.categories.id),
      name: service.categories.categname
    } : null,
    _count: {
      serviceOptions: service._count.serviceoptions,
      orderDetails: service._count.orderdetails
    }
  }

  return successResponse(transformedService)
})

// PUT /api/admin/services/[id] - Update a specific service
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const body = await request.json()

  // Check if service exists
  const existingService = await prisma.services.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingService) {
    throw new ApiError('Service not found', 404)
  }

  // If categoryId is being updated, check if the new category exists
  if (body.categoryId) {
    const category = await prisma.categories.findUnique({
      where: { id: BigInt(body.categoryId) },
    })

    if (!category) {
      throw new ApiError('Category not found', 404)
    }
  }

  // Transform frontend data to database field names
  const updateData: any = {
    updatedat: new Date()
  }

  if (body.categoryId !== undefined) updateData.categid = BigInt(body.categoryId)
  if (body.name !== undefined) updateData.name = body.name
  if (body.description !== undefined) updateData.description = body.description
  if (body.iconClass !== undefined) updateData.iconclass = body.iconClass
  if (body.price !== undefined) updateData.price = body.price
  if (body.discountRate !== undefined) updateData.discountrate = body.discountRate
  if (body.totalDiscount !== undefined) updateData.totaldiscount = body.totalDiscount
  if (body.manager !== undefined) updateData.manager = body.manager
  if (body.isActive !== undefined) updateData.isactive = body.isActive
  if (body.displayOrder !== undefined) updateData.displayorder = body.displayOrder

  const updatedService = await prisma.services.update({
    where: { id: BigInt(id) },
    data: updateData,
    include: {
      categories: {
        select: {
          id: true,
          categname: true,
          categdesc: true,
        },
      },
      serviceoptions: {
        select: {
          id: true,
          optname: true,
          optprice: true,
        },
      },
      _count: {
        select: {
          orderdetails: true,
          serviceoptions: true,
        },
      },
    },
  })

  // Transform the response data
  const transformedService = {
    id: String(updatedService.id),
    categoryId: String(updatedService.categid),
    name: updatedService.name,
    description: updatedService.description,
    iconClass: updatedService.iconclass,
    price: updatedService.price,
    discountRate: updatedService.discountrate,
    totalDiscount: updatedService.totaldiscount,
    manager: updatedService.manager,
    isActive: updatedService.isactive,
    displayOrder: updatedService.displayorder,
    createdAt: updatedService.createdat,
    updatedAt: updatedService.updatedat,
    category: updatedService.categories ? {
      id: String(updatedService.categories.id),
      name: updatedService.categories.categname
    } : null,
    _count: {
      serviceOptions: updatedService._count.serviceoptions,
      orderDetails: updatedService._count.orderdetails
    }
  }

  return successResponse(transformedService, 'Service updated successfully')
})

// PATCH /api/admin/services/[id] - Partial update (e.g., toggle status)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const body = await request.json()

  // Check if service exists
  const existingService = await prisma.services.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingService) {
    throw new ApiError('Service not found', 404)
  }

  // Allow only specific fields for PATCH
  const allowedFields = ['isactive', 'displayorder', 'isActive', 'displayOrder']
  const updateData: any = {}

  for (const [key, value] of Object.entries(body)) {
    if (allowedFields.includes(key)) {
      // Transform frontend field names to database field names
      if (key === 'isActive') {
        updateData['isactive'] = value
      } else if (key === 'displayOrder') {
        updateData['displayorder'] = value
      } else {
        updateData[key] = value
      }
    }
  }

  // Add timestamp
  if (Object.keys(updateData).length > 0) {
    updateData.updatedat = new Date()
  }

  if (Object.keys(updateData).length === 0) {
    throw new ApiError('No valid fields to update', 400)
  }

  const updatedService = await prisma.services.update({
    where: { id: BigInt(id) },
    data: updateData,
    include: {
      categories: {
        select: {
          id: true,
          categname: true,
          categdesc: true,
        },
      },
      serviceoptions: {
        select: {
          id: true,
          optname: true,
          optprice: true,
        },
      },
      _count: {
        select: {
          orderdetails: true,
          serviceoptions: true,
        },
      },
    },
  })

  // Transform the response data
  const transformedService = {
    id: String(updatedService.id),
    categoryId: String(updatedService.categid),
    name: updatedService.name,
    description: updatedService.description,
    iconClass: updatedService.iconclass,
    price: updatedService.price,
    discountRate: updatedService.discountrate,
    totalDiscount: updatedService.totaldiscount,
    manager: updatedService.manager,
    isActive: updatedService.isactive,
    displayOrder: updatedService.displayorder,
    createdAt: updatedService.createdat,
    updatedAt: updatedService.updatedat,
    category: updatedService.categories ? {
      id: String(updatedService.categories.id),
      name: updatedService.categories.categname
    } : null,
    _count: {
      serviceOptions: updatedService._count.serviceoptions,
      orderDetails: updatedService._count.orderdetails
    }
  }

  return successResponse(transformedService, 'Service updated successfully')
})

// DELETE /api/admin/services/[id] - Delete a specific service
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if service exists
  const existingService = await prisma.services.findUnique({
    where: { id: BigInt(id) },
    include: {
      orderdetails: { select: { id: true } },
      serviceoptions: { select: { id: true } },
    },
  })

  if (!existingService) {
    throw new ApiError('Service not found', 404)
  }

  // Check if service is being used
  if (existingService.orderdetails.length > 0) {
    throw new ApiError(
      'Cannot delete service that is used in orders. Please remove it from orders first.',
      400
    )
  }

  // Delete service options first (and their features)
  if (existingService.serviceoptions.length > 0) {
    // Delete service option features first
    await prisma.serviceoptionfeatures.deleteMany({
      where: {
        optid: {
          in: existingService.serviceoptions.map(opt => Number(opt.id))
        }
      },
    })

    // Then delete service options
    await prisma.serviceoptions.deleteMany({
      where: { servid: BigInt(id) },
    })
  }

  // Delete the service
  await prisma.services.delete({
    where: { id: BigInt(id) },
  })

  return successResponse(null, 'Service deleted successfully')
})
